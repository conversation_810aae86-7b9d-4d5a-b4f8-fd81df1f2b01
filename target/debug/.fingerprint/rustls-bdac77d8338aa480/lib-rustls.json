{"rustc": 8210029788606052455, "features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"default\", \"log\", \"logging\", \"prefer-post-quantum\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5073168975548574389, "path": 16207314485680614956, "deps": [[125823485620238427, "aws_lc_rs", false, 11833939031810203398], [2883436298747778685, "pki_types", false, 1494880960076982683], [3722963349756955755, "once_cell", false, 10388039721222781774], [5491919304041016563, "ring", false, 769365122951780118], [5986029879202738730, "log", false, 6278052248538945941], [6528079939221783635, "zeroize", false, 8535594212391546907], [7161480121686072451, "build_script_build", false, 11492571876665018891], [17003143334332120809, "subtle", false, 1921262131589761157], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 14020475662682824159]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-bdac77d8338aa480/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}