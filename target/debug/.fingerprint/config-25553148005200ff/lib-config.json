{"rustc": 8210029788606052455, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 18360497789134146168], [1238778183371849706, "yaml_rust2", false, 10742400036605282231], [2244620803250265856, "ron", false, 14328502315018731033], [2356429411733741858, "ini", false, 13463960467586097310], [6517602928339163454, "path<PERSON><PERSON>", false, 17391512722256634250], [9689903380558560274, "serde", false, 9369136447857529070], [11946729385090170470, "async_trait", false, 11442079438412259378], [13475460906694513802, "convert_case", false, 9992150549713752844], [14718834678227948963, "winnow", false, 1126674785273598766], [15367738274754116744, "serde_json", false, 5584086458392043189], [15609422047640926750, "toml", false, 13910779557977607447]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-25553148005200ff/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}