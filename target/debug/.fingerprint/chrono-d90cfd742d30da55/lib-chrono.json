{"rustc": 8210029788606052455, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 5347358027863023418, "path": 16800623322018478868, "deps": [[5157631553186200874, "num_traits", false, 3235153500961492600], [7910860254152155345, "iana_time_zone", false, 9690331076762036613], [9689903380558560274, "serde", false, 9369136447857529070]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-d90cfd742d30da55/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}