{"rustc": 8210029788606052455, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode-bool\", \"unicode-case\", \"unicode-perl\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 5347358027863023418, "path": 2365493837025371978, "deps": [[2779309023524819297, "aho_corasick", false, 1207667297570561248], [3129130049864710036, "memchr", false, 9158485742524197283], [9408802513701742484, "regex_syntax", false, 9225489336068315185]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-automata-ad3060a6c53a7f89/dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}