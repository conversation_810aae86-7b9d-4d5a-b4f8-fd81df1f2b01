{"rustc": 8210029788606052455, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"deflate\", \"gzip\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"tokio-util\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5347358027863023418, "path": 12360358679216226155, "deps": [[40386456601120721, "percent_encoding", false, 16200396926407406596], [95042085696191081, "ipnet", false, 4966343901837983500], [126872836426101300, "async_compression", false, 14459996055946067180], [264090853244900308, "sync_wrapper", false, 2984824239282244274], [784494742817713399, "tower_service", false, 11185230098109331927], [1044435446100926395, "hyper_rustls", false, 12759207619742114767], [1288403060204016458, "tokio_util", false, 18271767872135388745], [1906322745568073236, "pin_project_lite", false, 17404025490255456001], [3150220818285335163, "url", false, 827759838011662285], [3722963349756955755, "once_cell", false, 10388039721222781774], [4405182208873388884, "http", false, 1442642354893557727], [5986029879202738730, "log", false, 6278052248538945941], [7414427314941361239, "hyper", false, 8022812304743305457], [7620660491849607393, "futures_core", false, 16418044051151382260], [8915503303801890683, "http_body", false, 16163309908068316628], [9538054652646069845, "tokio", false, 4937859514322949489], [9689903380558560274, "serde", false, 9369136447857529070], [10229185211513642314, "mime", false, 1831068390321789054], [10629569228670356391, "futures_util", false, 4234798585982012534], [11107720164717273507, "system_configuration", false, 3123686142724221423], [11295624341523567602, "rustls", false, 276455634051930628], [13809605890706463735, "h2", false, 314922082903747257], [14564311161534545801, "encoding_rs", false, 3970884812963731328], [15367738274754116744, "serde_json", false, 5584086458392043189], [16066129441945555748, "bytes", false, 11841174785958436283], [16311359161338405624, "rustls_pemfile", false, 10821717709784816564], [16542808166767769916, "serde_urlencoded", false, 8360295304371458629], [16622232390123975175, "tokio_rustls", false, 11524809623351026578], [17652733826348741533, "webpki_roots", false, 7007470102576382522], [18066890886671768183, "base64", false, 1671929485726790462]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-a28f89044fc2fa76/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}