{"rustc": 8210029788606052455, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode-bool\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5347358027863023418, "path": 15033791335116528145, "deps": [[555019317135488525, "regex_automata", false, 14339879500425487027], [2779309023524819297, "aho_corasick", false, 1207667297570561248], [3129130049864710036, "memchr", false, 9158485742524197283], [9408802513701742484, "regex_syntax", false, 9225489336068315185]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-2396d148d3da9e39/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}