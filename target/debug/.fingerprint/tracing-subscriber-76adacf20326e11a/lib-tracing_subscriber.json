{"rustc": 8210029788606052455, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 1006155289083248400, "path": 17770270771451006161, "deps": [[1009387600818341822, "matchers", false, 17559526361288604665], [1017461770342116999, "sharded_slab", false, 14098362730089179634], [3424551429995674438, "tracing_core", false, 4176214150175256401], [3666196340704888985, "smallvec", false, 13553407503334431021], [3722963349756955755, "once_cell", false, 10388039721222781774], [6981130804689348050, "tracing_serde", false, 387388741119311782], [8606274917505247608, "tracing", false, 7636764663105917532], [8614575489689151157, "nu_ansi_term", false, 15427696946717672422], [9451456094439810778, "regex", false, 9709432070195738898], [9689903380558560274, "serde", false, 9369136447857529070], [10806489435541507125, "tracing_log", false, 6030356631153412954], [12427285511609802057, "thread_local", false, 14420678491334948443], [15367738274754116744, "serde_json", false, 5584086458392043189]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-76adacf20326e11a/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}